# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Go files
[*.go]
indent_style = tab
indent_size = 4

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# Shell scripts
[*.sh]
indent_style = space
indent_size = 2

# Dockerfile
[Dockerfile*]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false

# Makefile
[Makefile]
indent_style = tab

# CSV files
[*.csv]
trim_trailing_whitespace = false

# Configuration files
[*.{toml,ini}]
indent_style = space
indent_size = 2

# HTML/XML files
[*.{html,xml}]
indent_style = space
indent_size = 2

# SQL files
[*.sql]
indent_style = space
indent_size = 2

# Helm templates
[templates/**/*.yaml]
indent_style = space
indent_size = 2

# Helm charts
[helm/**/*.yaml]
indent_style = space
indent_size = 2
